use crate::config::file_config::ModelConfigEntry;
use crate::config::AppConfig;
use crate::editor::ProcessingSignal;
// use crate::files::file_handler::LabeledFile; // Marked as unused by compiler
// Unused imports removed: AutoExpertSwitch, AutoResearchMode
use crate::files::ordered_files::OrderedFiles; // Added for OrderedFiles
use crate::interactive::autocomplete::AutoCompleteState;
use crate::interactive::file_suggester::FileSuggesterState;
use crate::llm::ChatMessage;
use crate::sound::SoundSystem;
use crate::task::Task; // Added
                       // use std::collections::HashMap; // Marked as unused by compiler
                       // For EditPlanItem (EditTarget is part of its definition, but not directly used here)
use std::path::PathBuf;
use crate::tui::tui_logger::TuiLoggerState;

#[derive(Debug, PartialEq, Clone, Copy)]
pub enum InputMode {
    Normal,
    LoggerFocus,             // For when the logger panel is focused
    FilesFocus,              // For when the files panel is focused
    AwaitingRunConfirmation, // For y/n after /run command
    SearchForward,           // For search input when searching forward (/)
    SearchBackward,          // For search input when searching backward (?)
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum EditPlanItemStatus {
    Pending,
    Processing,
    Success,
    Failure,
}
#[derive(Debug, Clone)]
pub struct EditPlanItem {
    #[allow(dead_code)] // Field is currently unused but might be in the future
    pub path: PathBuf, // Store full path for potential future use, though only filename is displayed
    pub filename: String,
    pub start_line: usize,
    pub end_line: usize,
    pub status: EditPlanItemStatus,
}

pub struct InteractiveApp {
    pub input: String,
    pub input_mode: InputMode,
    pub tasks: Vec<Task>,
    // pub labeled_files_map: HashMap<PathBuf, LabeledFile>, // Replaced by ordered_files
    pub ordered_files: OrderedFiles, // Added
    pub focused_file_index: Option<usize>,
    pub should_quit: bool,
    pub is_processing: bool,
    pub is_auto_researching: bool, // Added for auto-research state
    pub is_asking_question: bool,  // Added for question state
    pub logger_state: TuiLoggerState,
    pub spinner_chars: Vec<char>,
    pub spinner_index: usize,
    pub processing_progress: f32,
    pub total_targets_for_progress: usize,
    pub show_success_state_until: Option<std::time::Instant>,
    pub current_task_abort_handle: Option<tokio::task::AbortHandle>, // For cancelling the current task

    // For input history
    pub input_history: Vec<String>,
    pub current_history_index: Option<usize>,
    pub current_input_draft: String,
    pub input_cursor_char_idx: usize, // Logical cursor position in app.input
    pub timestamps_enabled: bool,     // For TUI logger
    pub current_path: PathBuf,        // Current working directory
    pub app_config: AppConfig,        // Mutable AppConfig
    pub sound_system: Option<SoundSystem>, // Sound system for audio feedback
    pub autocomplete_state: AutoCompleteState,
    pub file_suggester_state: FileSuggesterState,
    pub model_suggester_state: ModelSuggesterState, // Added model suggester state
    pub number_accumulator: String, // For numbered jumps like 10j, 25k
    pub pending_g_press: Option<std::time::Instant>, // For gg navigation
    pub auto_research_mode_suggester_state: AutoResearchModeSuggesterState, // Added
    pub auto_expert_switch_suggester_state: AutoExpertSwitchSuggesterState, // Added
    pub files_scroll_offset: usize,                 // For vertical scrolling the files list
    pub files_horizontal_scroll_offset: isize,      // For horizontal scrolling of filenames

    // For /run command
    pub last_run_command_output: Option<String>,
    pub last_run_command_executed: Option<String>, // Added to store the command itself

    pub current_task_start_time: Option<std::time::Instant>,
    pub task_retry_count: usize,

    // Research-specific state
    pub is_researching: bool,
    pub research_progress: f32, // Placeholder for potential progress bar
    pub current_research_task_id: Option<String>,
    pub current_research_abort_handle: Option<tokio::task::AbortHandle>,

    // Editing Plan state
    pub editing_plan_items: Vec<EditPlanItem>,
    pub current_processing_target_index: Option<usize>,

    // For tracking total time across retries
    pub original_task_start_time: Option<std::time::Instant>,

    // For storing file categorization from research - Removed, managed by OrderedFiles or signals
    // pub current_categorization: Option<crate::research::types::CategorizedFilePaths>,

    // For storing question/answer history
    pub question_answer_history: Vec<QuestionAnswerPair>,

    // For tracking the last significant command type for simple output
    pub last_significant_command: Option<LastSignificantCommandType>,
    pub research_bash_commands_issued: usize, // Added to track bash commands during research
    #[allow(dead_code)]
    pub pending_success_data_for_autotest: Option<crate::editor::ProcessingSuccess>, // For storing success data if auto-test is pending
    pub original_user_request_for_current_processing_sequence: Option<String>, // Added for pristine request tracking
    pub pruned_tasks_summary_messages: Vec<ChatMessage>, // For storing summaries of pruned tasks
    pub last_completed_task: Option<Task>,                 // Added for /continue command
    pub current_operation_model_alias: Option<String>, // Added to track current model alias for TUI
}

#[derive(Debug, Clone, Copy)] // Added Copy for LastSignificantCommandType
pub enum LastSignificantCommandType {
    Edit,
    Research,
    Question,
}

#[derive(Debug, Clone, PartialEq)] // Added PartialEq
pub struct QuestionAnswerPair {
    pub question: String,
    pub answer: String,
}

impl InteractiveApp {
    // Constructor now takes initial state.
    // Actual loading of history and files will happen in `runner.rs`
    // and then passed to this constructor.
    pub fn new(
        initial_tasks: Vec<Task>,
        initial_qna_history: Vec<QuestionAnswerPair>,
        initial_input_history: Vec<String>,
        initial_ordered_files: OrderedFiles,
        initial_prompt: String,
        base_app_config: AppConfig,
    ) -> Self {
        let current_path = std::env::current_dir().unwrap_or_else(|e| {
            log::error!("Failed to get current directory: {}. Using default.", e);
            PathBuf::from(".")
        });
        let mut app_config = base_app_config.clone();
        app_config.user_prompt = initial_prompt.clone();

        let logger_state = TuiLoggerState::new();
        let app = InteractiveApp {
            input: initial_prompt,
            input_mode: InputMode::Normal,
            tasks: initial_tasks,
            ordered_files: initial_ordered_files, // Use passed-in instance
            focused_file_index: None,
            should_quit: false,
            is_processing: false,
            is_auto_researching: false, // Initialize auto-research state
            is_asking_question: false,  // Initialize question state
            logger_state,               // Use the new TuiLoggerState
            spinner_chars: vec!['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
            spinner_index: 0,
            processing_progress: 0.0,
            total_targets_for_progress: 0,
            show_success_state_until: None,
            current_task_abort_handle: None,
            input_history: initial_input_history,
            current_history_index: None,
            current_input_draft: String::new(),
            input_cursor_char_idx: 0, // Initial cursor position for the input field
            timestamps_enabled: app_config.timestamps,
            current_path,
            app_config, // app_config is now fully initialized
            sound_system: SoundSystem::new().ok(), // Initialize sound system, ignore errors
            autocomplete_state: AutoCompleteState::new(),
            file_suggester_state: FileSuggesterState::new(),
            model_suggester_state: ModelSuggesterState::new(), // Initialize new state
            auto_research_mode_suggester_state: AutoResearchModeSuggesterState::new(), // Initialize
            auto_expert_switch_suggester_state: AutoExpertSwitchSuggesterState::new(), // Initialize
            files_scroll_offset: 0,
            files_horizontal_scroll_offset: 0, // Initialize as isize
            last_run_command_output: None,
            last_run_command_executed: None,
            current_task_start_time: None,
            task_retry_count: 0,
            // research_tasks: initial_research_tasks, // Removed research_tasks initialization
            is_researching: false,
            research_progress: 0.0,
            current_research_task_id: None,
            current_research_abort_handle: None,
            editing_plan_items: Vec::new(),
            current_processing_target_index: None,
            original_task_start_time: None,
            // current_categorization: None, // Removed
            question_answer_history: initial_qna_history,
            last_significant_command: None,
            research_bash_commands_issued: 0,
            pending_success_data_for_autotest: None, // Initialize new field
            original_user_request_for_current_processing_sequence: None, // Initialize new field
            pruned_tasks_summary_messages: Vec::new(), // Initialize new field
            last_completed_task: None, // Initialize new field for /continue
            current_operation_model_alias: Some(base_app_config.default_model.clone()), // Initialize with default
            number_accumulator: String::new(), // Initialize number accumulator for numbered jumps
            pending_g_press: None, // Initialize gg navigation state
        };

        // If initial_tasks is empty and app.input (from CLI) is not, create the first task.
        // This logic is better handled in `interactive::runner` before calling `InteractiveApp::new`
        // or immediately after, to ensure `app.tasks` is correctly populated for the first operation.
        // For now, `submit_current_input` will handle creating a task if `app.input` is set.

        app
    }

    // This is the static helper discussed. Could be moved to `notifications.rs` if preferred.
    // For now, keeping it here to avoid adding another file to the chat if not strictly necessary.
    // However, the user's request implies creating `notifications.rs`, so this might be redundant
    // if the one in `notifications.rs` is used everywhere.
    // Let's assume the one in `notifications.rs` is the primary one. This can be removed.
    /*
    pub fn execute_notification_command_static(command_str: &str, cwd: &PathBuf, title: &str, contents: &str) {
        if command_str.is_empty() {
            return;
        }
        // ... (implementation as planned, using spawn_blocking)
    }
    */

    pub fn get_full_conversation_history_for_llm(&self) -> Vec<ChatMessage> {
        self.tasks
            .iter()
            .flat_map(|task| task.messages.clone())
            .collect()
    }

    pub fn current_task_mut(&mut self) -> Option<&mut Task> {
        self.tasks.last_mut()
    }
    // Removed get_current_task_messages_clone as it's unused.

    pub fn get_historical_context_for_llm(&self) -> Vec<ChatMessage> {
        if self.tasks.len() <= 1 {
            Vec::new()
        } else {
            self.tasks
                .iter()
                .take(self.tasks.len() - 1)
                .flat_map(|task| task.messages.clone())
                .collect()
        }
    }

    pub fn submit_current_input(&mut self) {
        if self.input.is_empty() {
            self.is_processing = false; // Ensure not stuck in processing if input is empty
            return;
        }

        // Play submit sound
        if let Some(ref sound_system) = self.sound_system {
            sound_system.play_submit_sound(&self.app_config);
        }

        let pure_user_request = self.input.clone(); // This is the raw input from the user

        // Determine if this is the first task overall for generating the files message
        // Note: handled in runner.rs, InteractiveApp::submit_current_input primarily sets up state

        self.app_config.user_prompt = pure_user_request.clone(); // Store the pure request

        // Add to input history if it's new
        if !pure_user_request.is_empty() && self.input_history.last() != Some(&pure_user_request) {
            self.input_history.push(self.input.clone());
        }
        self.current_history_index = None; // Reset history navigation
        self.current_input_draft.clear(); // Clear draft

        self.is_processing = true;
        self.processing_progress = 0.0; // Reset progress for new operation
        self.show_success_state_until = None; // Clear any lingering success display

        // Reset task retry count at new task start
        self.task_retry_count = 0;
        self.original_task_start_time = Some(std::time::Instant::now()); // Set original start time

        self.original_user_request_for_current_processing_sequence =
            Some(pure_user_request.clone()); // Store pristine request

        // Logging of the prompt will be done in runner.rs once the full task messages are constructed.
        let mut capped_input_for_log = pure_user_request.chars().take(100).collect::<String>();
        if pure_user_request.chars().count() > 100 {
            capped_input_for_log.push_str("...");
        }
        if self.app_config.log_level == crate::logger::LogLevel::Debug
            || self.app_config.log_level == crate::logger::LogLevel::Trace
        {
            log::debug!("{}", "-".repeat(70));
        }
        log::info!("Starting new task: \"{}...\"", capped_input_for_log);
        self.current_task_start_time = Some(std::time::Instant::now());

        // Notification Hook 1: Task Started (called from submit_current_input)
        let truncated_prompt_for_notification =
            crate::notifications::truncate_with_ellipsis(&pure_user_request, 100);
        crate::notifications::execute_notification_command(
            &self.app_config.notification_command,
            &self.current_path,
            "🖥️ LLEdit Task Started",
            &format!(r#""{}""#, truncated_prompt_for_notification),
        );
    }

    pub fn adjust_files_scroll(&mut self, list_len: usize, max_visible_items: usize) {
        if list_len == 0 || max_visible_items == 0 {
            self.files_scroll_offset = 0;
            if list_len == 0 {
                self.focused_file_index = None;
            }
            // Reset horizontal scroll if no files or no focus possible
            self.files_horizontal_scroll_offset = 0;
            return;
        }

        if let Some(selected_idx) = self.focused_file_index {
            // Ensure selected_idx is within bounds if list_len changed
            let current_selected_idx = if selected_idx >= list_len {
                let new_idx = list_len - 1;
                self.focused_file_index = Some(new_idx);
                new_idx
            } else {
                selected_idx
            };

            // Vertical scroll adjustment
            if current_selected_idx < self.files_scroll_offset {
                self.files_scroll_offset = current_selected_idx;
            } else if current_selected_idx >= self.files_scroll_offset + max_visible_items {
                self.files_scroll_offset =
                    current_selected_idx.saturating_sub(max_visible_items - 1);
            }

            // Ensure vertical scroll_offset doesn't exceed possible values
            let max_possible_vertical_offset = list_len.saturating_sub(max_visible_items);
            if self.files_scroll_offset > max_possible_vertical_offset {
                self.files_scroll_offset = max_possible_vertical_offset;
            }
            // Ensure vertical scroll_offset is 0 if all items are visible
            if list_len <= max_visible_items {
                self.files_scroll_offset = 0;
            }
            // Horizontal scroll is user-controlled relative to filename, no specific clamping here based on filename length.
        } else {
            // If nothing is focused, reset vertical scroll to the top and horizontal scroll.
            self.files_scroll_offset = 0;
            self.files_horizontal_scroll_offset = 0;
        }
    }
    pub fn clear_task_history(&mut self) {
        self.tasks.clear();
        self.last_completed_task = None;
        self.pruned_tasks_summary_messages.clear();
    }

    pub fn clear_task_info(&mut self) {
        self.app_config.task_info = None;
    }

    pub fn clear_all_files(&mut self) {
        self.ordered_files.remove_all_files();
        self.focused_file_index = None;
        self.files_horizontal_scroll_offset = 0;
    }

    #[allow(clippy::unused_self)]
    pub fn clear_logs(&mut self) {
        self.logger_state.clear_messages();
    }

    pub fn clear_editing_plan(&mut self) {
        self.editing_plan_items.clear();
        self.current_processing_target_index = None;
    }

    pub fn is_processing(&self) -> bool {
        self.is_processing || self.is_researching || self.is_asking_question
    }

    /// Encapsulates the logic to start processing a new task, adding it to the history
    /// and spawning the async operation.
    pub async fn start_processing_task(
        &mut self,
        new_task: Task,
        operation_signal_tx: &tokio::sync::mpsc::Sender<ProcessingSignal>,
    ) {
        self.tasks.push(new_task.clone());

        let all_previous_tasks: Vec<Task> =
            self.tasks.iter().take(self.tasks.len() - 1).cloned().collect();

        let task_app_config = self.app_config.clone();
        let task_ordered_files_snapshot = self.ordered_files.clone();
        let initial_messages = new_task.messages.clone();

        // Set model alias for edit processing
        self.current_operation_model_alias = Some(self.app_config.default_model.clone());

        let abort_handle = crate::interactive::async_ops::spawn_new_edit_processing(
            task_app_config,
            new_task,
            self.pruned_tasks_summary_messages.clone(),
            all_previous_tasks,
            initial_messages,
            task_ordered_files_snapshot,
            self.current_path.clone(),
            operation_signal_tx.clone(),
            self.task_retry_count, // Pass current retry count
            None,
        );
        self.current_task_abort_handle = Some(abort_handle);
    }

    pub async fn handle_input(
        &mut self,
        input: String,
        operation_signal_tx: &tokio::sync::mpsc::Sender<ProcessingSignal>,
    ) {
        self.input = input;

        let _changed_files = self.ordered_files.refresh_all_files_from_filesystem().await;

        if crate::interactive::commands::handle_command_if_present(self, operation_signal_tx).await
        {
            // Command was handled.
        } else {
            // Treat as a new edit prompt
            self.last_significant_command = Some(LastSignificantCommandType::Edit);
            self.submit_current_input(); // Sets up is_processing, history, etc.

            let mut new_task = Task::new(
                self.app_config.user_prompt.clone(),
                self.app_config.task_info.clone(),
            );

            // Add files message to the new task
            let (files_msg_opt, _) =
                self.ordered_files.get_files_for_prompt_message(&self.current_path);
            if let Some(files_msg) = files_msg_opt {
                new_task.add_message(files_msg);
            }

            // Create and add the main user instruction message, including task_info if present
            let mut instruction_content_user = self.app_config.user_prompt.clone();
            if let Some(ti_content) = &new_task.initial_task_info {
                if !ti_content.trim().is_empty() {
                    instruction_content_user = format!(
                        "Important task context:\n---\n{}\n---\n\n{}",
                        ti_content.trim(),
                        instruction_content_user
                    );
                }
            }
            new_task.add_message(crate::llm::ChatMessage {
                role: crate::llm::ChatRole::User,
                content: instruction_content_user,
                message_type: crate::llm::MessageType::Text,
            });

            self.start_processing_task(new_task, operation_signal_tx).await;
        }
    }
}

// --- Model Suggester State ---
use fuzzy_matcher::skim::SkimMatcherV2;
use fuzzy_matcher::FuzzyMatcher; // Added for fuzzy search // Added for fuzzy search

const MAX_VISIBLE_MODEL_SUGGESTIONS: usize = 4;

#[derive(Debug, Clone)]
pub struct ModelSuggestion {
    pub alias: String,
    pub display_text: String, // Formatted string for display
}

#[derive(Debug, Clone)]
pub struct ModelSuggesterState {
    pub active: bool,
    pub all_models: Vec<ModelSuggestion>, // Stores all possible model suggestions from config
    pub filtered_suggestions: Vec<ModelSuggestion>,
    pub selected_index: Option<usize>, // Index within filtered_suggestions
    pub scroll_offset: usize,
    pub input_prefix_for_completion: String, // e.g., "/default-model-set "
    pub current_search_term: String,         // The alias part being typed by the user
}

impl ModelSuggesterState {
    pub fn new() -> Self {
        Self {
            active: false,
            all_models: Vec::new(),
            filtered_suggestions: Vec::new(),
            selected_index: None,
            scroll_offset: 0,
            input_prefix_for_completion: String::new(),
            current_search_term: String::new(),
        }
    }

    pub fn activate_or_update(
        &mut self,
        models_config_list: &Option<Vec<ModelConfigEntry>>,
        current_input_after_command: &str, // e.g., "my-ali"
        prefix_for_completion: String,     // e.g., "/default-model-set "
        include_default_option: bool,      // New parameter
    ) {
        // Deactivate if no models are configured, unless we specifically want to show the "default" option.
        if !include_default_option
            && (models_config_list.is_none() || models_config_list.as_ref().unwrap().is_empty())
        {
            self.deactivate();
            return;
        }

        // Populate all_models only if it's empty (first activation or config changed)
        // For simplicity, we assume models_config_list doesn't change during a session.
        // If it could, this logic would need to be smarter or all_models rebuilt.
        if self.all_models.is_empty() {
            if let Some(list) = models_config_list {
                self.all_models = list
                    .iter()
                    .map(|entry| {
                        let display_url = entry.provider_url.as_deref().unwrap_or("Hosted");
                        ModelSuggestion {
                            alias: entry.alias.clone(),
                            display_text: format!(
                                "{}: {} | {} | {}",
                                entry.alias, entry.provider, entry.model, display_url
                            ),
                        }
                    })
                    .collect();
            }
        }

        let new_search_term = current_input_after_command.trim_start().to_lowercase();

        if self.active
            && self.current_search_term == new_search_term
            && self.input_prefix_for_completion == prefix_for_completion
        {
            // No change in search term or prefix, no need to re-filter if already active.
            return;
        }

        self.active = true;
        self.input_prefix_for_completion = prefix_for_completion;
        self.current_search_term = new_search_term; // This is already lowercased

        let matcher = SkimMatcherV2::default();
        let mut suggestions_to_consider: Vec<ModelSuggestion> = self.all_models.clone();

        if include_default_option {
            suggestions_to_consider.insert(
                0, // Insert at the beginning
                ModelSuggestion {
                    alias: "default".to_string(),
                    display_text: "default: Use application default model".to_string(),
                },
            );
        }

        let mut scored_suggestions: Vec<(i64, ModelSuggestion)> = suggestions_to_consider
            .iter()
            .filter_map(|model_sugg| {
                // Match against the alias
                matcher
                    .fuzzy_match(&model_sugg.alias, &self.current_search_term)
                    .map(|score| (score, model_sugg.clone()))
            })
            .collect();

        // Sort by score in descending order
        scored_suggestions.sort_by(|a, b| b.0.cmp(&a.0));

        self.filtered_suggestions = scored_suggestions
            .into_iter()
            .map(|(_, sugg)| sugg)
            .collect();

        if self.filtered_suggestions.is_empty() {
            // If include_default_option was true and search term is empty, "default" should have been there.
            // This path means either no models and no default, or models + default but search term matches none.
            self.selected_index = None;
        } else {
            // Similar to command autocomplete, reset to the top match after filtering
            self.selected_index = Some(0);
        }
        self.scroll_offset = 0;
        self.adjust_scroll();
    }

    pub fn deactivate(&mut self) {
        self.active = false;
        self.selected_index = None;
        self.filtered_suggestions.clear();
        // self.all_models.clear(); // Don't clear all_models, it's built from config once.
        self.scroll_offset = 0;
        self.current_search_term.clear();
        self.input_prefix_for_completion.clear();
    }

    pub fn select_next(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index = match self.selected_index {
            Some(idx) => Some((idx + 1) % self.filtered_suggestions.len()),
            None => Some(0),
        };
        self.adjust_scroll();
    }

    pub fn select_previous(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index = match self.selected_index {
            Some(idx) => Some(if idx == 0 {
                self.filtered_suggestions.len() - 1
            } else {
                idx - 1
            }),
            None => Some(self.filtered_suggestions.len() - 1),
        };
        self.adjust_scroll();
    }

    fn adjust_scroll(&mut self) {
        if let Some(selected_idx) = self.selected_index {
            if selected_idx < self.scroll_offset {
                self.scroll_offset = selected_idx;
            } else if selected_idx >= self.scroll_offset + MAX_VISIBLE_MODEL_SUGGESTIONS {
                self.scroll_offset = selected_idx - MAX_VISIBLE_MODEL_SUGGESTIONS + 1;
            }
            let max_possible_offset = self
                .filtered_suggestions
                .len()
                .saturating_sub(MAX_VISIBLE_MODEL_SUGGESTIONS);
            if self.scroll_offset > max_possible_offset {
                self.scroll_offset = max_possible_offset;
            }
        } else {
            self.scroll_offset = 0;
        }
    }

    // Removed get_current_selected_alias_for_input as it's unused.

    pub fn get_current_selected_alias_only(&self) -> Option<String> {
        self.selected_index
            .and_then(|idx| self.filtered_suggestions.get(idx))
            .map(|suggestion| suggestion.alias.clone())
    }

    pub fn get_visible_suggestions(&self) -> Vec<&ModelSuggestion> {
        if !self.active || self.filtered_suggestions.is_empty() {
            return Vec::new();
        }
        self.filtered_suggestions
            .iter()
            .skip(self.scroll_offset)
            .take(MAX_VISIBLE_MODEL_SUGGESTIONS)
            .collect()
    }

    pub fn get_popup_height(&self) -> usize {
        if !self.active {
            return 0;
        }
        if self.filtered_suggestions.is_empty() && !self.current_search_term.is_empty() {
            return 1 + 2; // For "No matches" text + borders
        }
        if self.filtered_suggestions.is_empty()
            && self.current_search_term.is_empty()
            && self.all_models.is_empty()
        {
            return 1 + 2; // For "No models configured" text + borders
        }
        // Height for suggestions + 2 for top/bottom borders of the popup block
        self.get_visible_suggestions()
            .len()
            .min(MAX_VISIBLE_MODEL_SUGGESTIONS)
            + 2
    }
}

const MAX_VISIBLE_SUGGESTIONS: usize = 4; // Define the constant here

// --- AutoResearchMode Suggester State ---
#[derive(Debug, Clone)]
pub struct AutoResearchModeSuggestion {
    pub name: String, // e.g., "false", "expert-only"
    pub description: String,
}

const AUTO_RESEARCH_MODE_SUGGESTIONS: [(&str, &str); 9] = [
    (
        "false",
        "Disable auto-research for default LLM. Expert may research.",
    ),
    (
        "true",
        "Default LLM decides; mandatory if no files. Expert may research.",
    ),
    (
        "forced",
        "Force default LLM to perform research. Expert may research.",
    ),
    (
        "first",
        "Like 'true' for 1st attempt; 'false' on retries. Expert may research.",
    ),
    (
        "first-forced-then-false",
        "Like 'forced' for 1st attempt; 'false' on retries. Expert may research.",
    ),
    (
        "first-forced-then-true",
        "Like 'forced' for 1st attempt; 'true' on retries. Expert may research.",
    ),
    (
        "expert-only",
        "Default LLM doesn't research. Expert LLM may research.",
    ),
    (
        "no-files",
        "Default LLM researches if no files. Expert LLM cannot research.",
    ),
    (
        "expert-and-no-files-only",
        "Default LLM researches if no files. Expert LLM can research.",
    ),
];

#[derive(Debug, Clone)]
pub struct AutoResearchModeSuggesterState {
    pub active: bool,
    all_suggestions: Vec<AutoResearchModeSuggestion>,
    pub filtered_suggestions: Vec<AutoResearchModeSuggestion>,
    pub selected_index: Option<usize>,
    pub scroll_offset: usize,
    pub input_prefix_for_completion: String,
    pub current_search_term: String,
}

impl AutoResearchModeSuggesterState {
    pub fn new() -> Self {
        let all_suggestions = AUTO_RESEARCH_MODE_SUGGESTIONS
            .iter()
            .map(|(name, desc)| AutoResearchModeSuggestion {
                name: name.to_string(),
                description: desc.to_string(),
            })
            .collect();
        Self {
            active: false,
            all_suggestions,
            filtered_suggestions: Vec::new(),
            selected_index: None,
            scroll_offset: 0,
            input_prefix_for_completion: String::new(),
            current_search_term: String::new(),
        }
    }

    pub fn activate_or_update(
        &mut self,
        current_input_after_command: &str,
        prefix_for_completion: String,
    ) {
        let new_search_term = current_input_after_command.trim_start().to_lowercase();
        if self.active
            && self.current_search_term == new_search_term
            && self.input_prefix_for_completion == prefix_for_completion
        {
            return;
        }

        self.active = true;
        self.input_prefix_for_completion = prefix_for_completion;
        self.current_search_term = new_search_term;

        let matcher = SkimMatcherV2::default();
        if self.current_search_term.is_empty() {
            self.filtered_suggestions = self.all_suggestions.clone();
        } else {
            let mut scored_suggestions: Vec<(i64, AutoResearchModeSuggestion)> = self
                .all_suggestions
                .iter()
                .filter_map(|sugg| {
                    matcher
                        .fuzzy_match(&sugg.name, &self.current_search_term)
                        .map(|score| (score, sugg.clone()))
                })
                .collect();
            scored_suggestions.sort_by(|a, b| b.0.cmp(&a.0));
            self.filtered_suggestions = scored_suggestions
                .into_iter()
                .map(|(_, sugg)| sugg)
                .collect();
        }

        self.selected_index = if self.filtered_suggestions.is_empty() {
            None
        } else {
            Some(0)
        };
        self.scroll_offset = 0;
        self.adjust_scroll();
    }

    pub fn deactivate(&mut self) {
        self.active = false;
        self.selected_index = None;
        self.filtered_suggestions.clear();
        self.scroll_offset = 0;
        self.current_search_term.clear();
        self.input_prefix_for_completion.clear();
    }

    pub fn select_next(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index = self.selected_index.map_or(Some(0), |idx| {
            Some((idx + 1) % self.filtered_suggestions.len())
        });
        self.adjust_scroll();
    }

    pub fn select_previous(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index =
            self.selected_index
                .map_or(Some(self.filtered_suggestions.len() - 1), |idx| {
                    Some(if idx == 0 {
                        self.filtered_suggestions.len() - 1
                    } else {
                        idx - 1
                    })
                });
        self.adjust_scroll();
    }

    fn adjust_scroll(&mut self) {
        if let Some(selected_idx) = self.selected_index {
            if selected_idx < self.scroll_offset {
                self.scroll_offset = selected_idx;
            } else if selected_idx >= self.scroll_offset + MAX_VISIBLE_SUGGESTIONS {
                self.scroll_offset = selected_idx - MAX_VISIBLE_SUGGESTIONS + 1;
            }
            let max_possible_offset = self
                .filtered_suggestions
                .len()
                .saturating_sub(MAX_VISIBLE_SUGGESTIONS);
            if self.scroll_offset > max_possible_offset {
                self.scroll_offset = max_possible_offset;
            }
        } else {
            self.scroll_offset = 0;
        }
    }

    pub fn get_current_selected_name_for_input(&self) -> Option<String> {
        self.selected_index
            .and_then(|idx| self.filtered_suggestions.get(idx))
            .map(|sugg| sugg.name.clone())
    }

    pub fn get_visible_suggestions(&self) -> Vec<&AutoResearchModeSuggestion> {
        if !self.active || self.filtered_suggestions.is_empty() {
            return Vec::new();
        }
        self.filtered_suggestions
            .iter()
            .skip(self.scroll_offset)
            .take(MAX_VISIBLE_SUGGESTIONS)
            .collect()
    }

    pub fn get_popup_height(&self) -> usize {
        if !self.active {
            return 0;
        }
        if self.filtered_suggestions.is_empty() && !self.current_search_term.is_empty() {
            return 1 + 2;
        } // "No matches" + borders
        self.get_visible_suggestions()
            .len()
            .min(MAX_VISIBLE_SUGGESTIONS)
            + 2
    }
}

// --- AutoExpertSwitch Suggester State ---
#[derive(Debug, Clone)]
pub struct AutoExpertSwitchSuggestion {
    pub name: String, // e.g., "false", "first-forced-then-true"
    pub description: String,
}

const AUTO_EXPERT_SWITCH_SUGGESTIONS: [(&str, &str); 6] = [
    ("false", "Never run auto-expert."),
    ("true", "LLM decides if auto-expert is needed."),
    ("forced", "Always run auto-expert."),
    ("first", "LLM decides, but only on the first run of a task."),
    (
        "first-forced-then-false",
        "Always run on first run, then False on retries.",
    ),
    (
        "first-forced-then-true",
        "Always run on first run, then True on retries.",
    ),
];

#[derive(Debug, Clone)]
pub struct AutoExpertSwitchSuggesterState {
    pub active: bool,
    all_suggestions: Vec<AutoExpertSwitchSuggestion>,
    pub filtered_suggestions: Vec<AutoExpertSwitchSuggestion>,
    pub selected_index: Option<usize>,
    pub scroll_offset: usize,
    pub input_prefix_for_completion: String,
    pub current_search_term: String,
}

impl AutoExpertSwitchSuggesterState {
    pub fn new() -> Self {
        let all_suggestions = AUTO_EXPERT_SWITCH_SUGGESTIONS
            .iter()
            .map(|(name, desc)| AutoExpertSwitchSuggestion {
                name: name.to_string(),
                description: desc.to_string(),
            })
            .collect();
        Self {
            active: false,
            all_suggestions,
            filtered_suggestions: Vec::new(),
            selected_index: None,
            scroll_offset: 0,
            input_prefix_for_completion: String::new(),
            current_search_term: String::new(),
        }
    }

    pub fn activate_or_update(
        &mut self,
        current_input_after_command: &str,
        prefix_for_completion: String,
    ) {
        let new_search_term = current_input_after_command.trim_start().to_lowercase();
        if self.active
            && self.current_search_term == new_search_term
            && self.input_prefix_for_completion == prefix_for_completion
        {
            return;
        }

        self.active = true;
        self.input_prefix_for_completion = prefix_for_completion;
        self.current_search_term = new_search_term;

        let matcher = SkimMatcherV2::default();
        if self.current_search_term.is_empty() {
            self.filtered_suggestions = self.all_suggestions.clone();
        } else {
            let mut scored_suggestions: Vec<(i64, AutoExpertSwitchSuggestion)> = self
                .all_suggestions
                .iter()
                .filter_map(|sugg| {
                    matcher
                        .fuzzy_match(&sugg.name, &self.current_search_term)
                        .map(|score| (score, sugg.clone()))
                })
                .collect();
            scored_suggestions.sort_by(|a, b| b.0.cmp(&a.0));
            self.filtered_suggestions = scored_suggestions
                .into_iter()
                .map(|(_, sugg)| sugg)
                .collect();
        }

        self.selected_index = if self.filtered_suggestions.is_empty() {
            None
        } else {
            Some(0)
        };
        self.scroll_offset = 0;
        self.adjust_scroll();
    }

    pub fn deactivate(&mut self) {
        self.active = false;
        self.selected_index = None;
        self.filtered_suggestions.clear();
        self.scroll_offset = 0;
        self.current_search_term.clear();
        self.input_prefix_for_completion.clear();
    }

    pub fn select_next(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index = self.selected_index.map_or(Some(0), |idx| {
            Some((idx + 1) % self.filtered_suggestions.len())
        });
        self.adjust_scroll();
    }

    pub fn select_previous(&mut self) {
        if self.filtered_suggestions.is_empty() {
            return;
        }
        self.selected_index =
            self.selected_index
                .map_or(Some(self.filtered_suggestions.len() - 1), |idx| {
                    Some(if idx == 0 {
                        self.filtered_suggestions.len() - 1
                    } else {
                        idx - 1
                    })
                });
        self.adjust_scroll();
    }

    fn adjust_scroll(&mut self) {
        if let Some(selected_idx) = self.selected_index {
            if selected_idx < self.scroll_offset {
                self.scroll_offset = selected_idx;
            } else if selected_idx >= self.scroll_offset + MAX_VISIBLE_SUGGESTIONS {
                self.scroll_offset = selected_idx - MAX_VISIBLE_SUGGESTIONS + 1;
            }
            let max_possible_offset = self
                .filtered_suggestions
                .len()
                .saturating_sub(MAX_VISIBLE_SUGGESTIONS);
            if self.scroll_offset > max_possible_offset {
                self.scroll_offset = max_possible_offset;
            }
        } else {
            self.scroll_offset = 0;
        }
    }

    pub fn get_current_selected_name_for_input(&self) -> Option<String> {
        self.selected_index
            .and_then(|idx| self.filtered_suggestions.get(idx))
            .map(|sugg| sugg.name.clone())
    }

    pub fn get_visible_suggestions(&self) -> Vec<&AutoExpertSwitchSuggestion> {
        if !self.active || self.filtered_suggestions.is_empty() {
            return Vec::new();
        }
        self.filtered_suggestions
            .iter()
            .skip(self.scroll_offset)
            .take(MAX_VISIBLE_SUGGESTIONS)
            .collect()
    }

    pub fn get_popup_height(&self) -> usize {
        if !self.active {
            return 0;
        }
        if self.filtered_suggestions.is_empty() && !self.current_search_term.is_empty() {
            return 1 + 2;
        } // "No matches" + borders
        self.get_visible_suggestions()
            .len()
            .min(MAX_VISIBLE_SUGGESTIONS)
            + 2
    }
}
